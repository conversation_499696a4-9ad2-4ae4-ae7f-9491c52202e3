package cn.ilikexff.ultimate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * QQ登录请求DTO
 */
@Data
@Schema(description = "QQ登录请求")
public class QQLoginRequest {
    
    @NotBlank(message = "授权码不能为空")
    @Schema(description = "QQ授权码", example = "authorization_code_from_qq")
    private String code;
    
    @Schema(description = "回调地址", example = "http://localhost:3000/auth/qq/callback")
    private String redirectUri;
}
