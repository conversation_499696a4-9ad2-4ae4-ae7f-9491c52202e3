package cn.ilikexff.ultimate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 绑定账号请求DTO
 */
@Data
@Schema(description = "绑定账号请求")
public class BindAccountRequest {
    
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "***********")
    private String phoneNumber;
    
    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码", example = "123456")
    private String verificationCode;
    
    @Schema(description = "绑定类型：EMAIL/PHONE", example = "EMAIL")
    private String bindType;
}
