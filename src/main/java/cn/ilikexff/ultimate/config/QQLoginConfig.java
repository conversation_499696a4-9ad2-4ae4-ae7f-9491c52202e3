package cn.ilikexff.ultimate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * QQ登录配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "qq.login")
public class QQLoginConfig {
    
    /**
     * QQ应用ID
     */
    private String appId;
    
    /**
     * QQ应用密钥
     */
    private String appKey;
    
    /**
     * 授权回调地址
     */
    private String redirectUri;
    
    /**
     * 授权范围
     */
    private String scope = "get_user_info";
    
    /**
     * QQ授权地址
     */
    private String authorizeUrl = "https://graph.qq.com/oauth2.0/authorize";
    
    /**
     * 获取Access Token地址
     */
    private String accessTokenUrl = "https://graph.qq.com/oauth2.0/token";
    
    /**
     * 获取OpenID地址
     */
    private String openIdUrl = "https://graph.qq.com/oauth2.0/me";
    
    /**
     * 获取用户信息地址
     */
    private String userInfoUrl = "https://graph.qq.com/user/get_user_info";
}
