package cn.ilikexff.ultimate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Ultimate业务配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "ultimate")
public class UltimateConfig {

    /**
     * 注册奖励U币
     */
    private Integer registerRewardCoins = 100;

    /**
     * 每日签到基础奖励
     */
    private Integer dailySigninBaseReward = 10;

    /**
     * 每日一推额外奖励倍数
     */
    private Double dailyPuzzleBonusMultiplier = 1.5;

    /**
     * 内测配置
     */
    private BetaConfig beta = new BetaConfig();

    /**
     * 邀请码配置
     */
    private InvitationConfig invitation = new InvitationConfig();

    @Data
    public static class BetaConfig {
        /**
         * 是否开启内测模式
         */
        private Boolean enabled = false;

        /**
         * 内测码列表
         */
        private List<String> codes;
    }

    @Data
    public static class InvitationConfig {
        /**
         * 邀请奖励U币（邀请者获得的奖励）
         */
        private Integer rewardCoins = 200;

        /**
         * 被邀请者奖励比例（邀请者奖励的百分比）
         */
        private Double inviteeRewardRatio = 0.25;
    }
}
