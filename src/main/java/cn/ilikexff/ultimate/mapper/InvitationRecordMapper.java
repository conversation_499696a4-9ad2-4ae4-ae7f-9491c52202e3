package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.InvitationRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 邀请记录Mapper
 */
@Mapper
public interface InvitationRecordMapper {

    /**
     * 插入邀请记录
     */
    @Insert("INSERT INTO invitation_records (invitation_id, inviter_user_id, invitee_user_id, inviter_reward, invitee_reward, reward_status, created_at, updated_at, deleted) " +
            "VALUES (#{invitationId}, #{inviterUserId}, #{inviteeUserId}, #{inviterReward}, #{inviteeReward}, #{rewardStatus}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "recordId")
    int insert(InvitationRecord record);

    /**
     * 根据被邀请者ID查找记录
     */
    @Select("SELECT * FROM invitation_records WHERE invitee_user_id = #{inviteeUserId} AND deleted = false")
    InvitationRecord findByInviteeUserId(Long inviteeUserId);

    /**
     * 根据邀请者ID查找记录列表
     */
    @Select("SELECT ir.*, u.username as invitee_username, u.nickname as invitee_nickname " +
            "FROM invitation_records ir " +
            "LEFT JOIN users u ON ir.invitee_user_id = u.user_id " +
            "WHERE ir.inviter_user_id = #{inviterUserId} AND ir.deleted = false " +
            "ORDER BY ir.created_at DESC")
    List<InvitationRecord> findByInviterUserId(Long inviterUserId);

    /**
     * 更新奖励状态
     */
    @Update("UPDATE invitation_records SET reward_status = #{rewardStatus}, updated_at = NOW() WHERE record_id = #{recordId}")
    int updateRewardStatus(Long recordId, String rewardStatus);

    /**
     * 获取邀请者的总奖励统计
     */
    @Select("SELECT " +
            "COUNT(*) as total_invitations, " +
            "COALESCE(SUM(inviter_reward), 0) as total_rewards " +
            "FROM invitation_records " +
            "WHERE inviter_user_id = #{inviterUserId} AND reward_status = 'COMPLETED' AND deleted = false")
    @Results({
        @Result(property = "totalInvitations", column = "total_invitations"),
        @Result(property = "totalRewards", column = "total_rewards")
    })
    InvitationRewardStatistics getInviterRewardStatistics(Long inviterUserId);

    /**
     * 奖励统计内部类
     */
    class InvitationRewardStatistics {
        private Integer totalInvitations;
        private Integer totalRewards;

        public Integer getTotalInvitations() {
            return totalInvitations;
        }

        public void setTotalInvitations(Integer totalInvitations) {
            this.totalInvitations = totalInvitations;
        }

        public Integer getTotalRewards() {
            return totalRewards;
        }

        public void setTotalRewards(Integer totalRewards) {
            this.totalRewards = totalRewards;
        }
    }
}
