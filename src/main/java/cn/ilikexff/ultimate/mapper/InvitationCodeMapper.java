package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.InvitationCode;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 邀请码Mapper
 */
@Mapper
public interface InvitationCodeMapper {

    /**
     * 插入邀请码
     */
    @Insert("INSERT INTO invitation_codes (code, inviter_user_id, used_count, max_uses, is_active, expires_at, created_at, updated_at, deleted) " +
            "VALUES (#{code}, #{inviterUserId}, #{usedCount}, #{maxUses}, #{isActive}, #{expiresAt}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "invitationId")
    int insert(InvitationCode invitationCode);

    /**
     * 根据邀请码查找
     */
    @Select("SELECT * FROM invitation_codes WHERE code = #{code} AND deleted = false")
    InvitationCode findByCode(String code);

    /**
     * 根据用户ID查找邀请码列表
     */
    @Select("SELECT * FROM invitation_codes WHERE inviter_user_id = #{userId} AND deleted = false ORDER BY created_at DESC")
    List<InvitationCode> findByInviterUserId(Long userId);

    /**
     * 更新邀请码使用次数
     */
    @Update("UPDATE invitation_codes SET used_count = used_count + 1, updated_at = NOW() WHERE invitation_id = #{invitationId}")
    int incrementUsedCount(Long invitationId);

    /**
     * 更新邀请码状态
     */
    @Update("UPDATE invitation_codes SET is_active = #{isActive}, updated_at = NOW() WHERE invitation_id = #{invitationId}")
    int updateStatus(Long invitationId, Boolean isActive);

    /**
     * 检查邀请码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM invitation_codes WHERE code = #{code} AND deleted = false")
    boolean existsByCode(String code);

    /**
     * 获取用户的邀请统计
     */
    @Select("SELECT " +
            "COUNT(*) as total_invitations, " +
            "SUM(CASE WHEN ir.invitee_user_id IS NOT NULL THEN 1 ELSE 0 END) as successful_invitations " +
            "FROM invitation_codes ic " +
            "LEFT JOIN invitation_records ir ON ic.invitation_id = ir.invitation_id " +
            "WHERE ic.inviter_user_id = #{userId} AND ic.deleted = false")
    @Results({
        @Result(property = "totalInvitations", column = "total_invitations"),
        @Result(property = "successfulInvitations", column = "successful_invitations")
    })
    InvitationStatistics getInvitationStatistics(Long userId);

    /**
     * 邀请统计内部类
     */
    class InvitationStatistics {
        private Integer totalInvitations;
        private Integer successfulInvitations;

        public Integer getTotalInvitations() {
            return totalInvitations;
        }

        public void setTotalInvitations(Integer totalInvitations) {
            this.totalInvitations = totalInvitations;
        }

        public Integer getSuccessfulInvitations() {
            return successfulInvitations;
        }

        public void setSuccessfulInvitations(Integer successfulInvitations) {
            this.successfulInvitations = successfulInvitations;
        }
    }
}
