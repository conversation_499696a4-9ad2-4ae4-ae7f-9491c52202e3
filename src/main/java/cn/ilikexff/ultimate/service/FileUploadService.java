package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件上传服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadService {

    private final UserService userService;

    @Value("${file.upload.path:/tmp/ultimate/uploads}")
    private String uploadPath;

    @Value("${file.upload.base-url:http://localhost:8080/uploads}")
    private String baseUrl;

    // 支持的图片格式
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    // 支持的文件扩展名
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".webp"
    );

    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传用户头像
     */
    public String uploadAvatar(MultipartFile file, Long userId) {
        // 验证文件
        validateImageFile(file);

        // 生成文件名和路径
        String fileName = generateFileName(file, "avatar");
        String relativePath = "avatars/" + fileName;
        
        // 保存文件
        String fullPath = saveFile(file, relativePath);
        
        // 生成访问URL
        String avatarUrl = baseUrl + "/" + relativePath;
        
        // 更新用户头像URL
        userService.updateUserInfo(userId, null, avatarUrl);
        
        log.info("Avatar uploaded for user {}: {}", userId, avatarUrl);
        return avatarUrl;
    }

    /**
     * 上传谜题图片
     */
    public String uploadPuzzleImage(MultipartFile file, Long userId, Long puzzleId) {
        // 验证文件
        validateImageFile(file);

        // 生成文件名和路径
        String fileName = generateFileName(file, "puzzle");
        String relativePath = "puzzles/" + fileName;
        
        // 保存文件
        String fullPath = saveFile(file, relativePath);
        
        // 生成访问URL
        String imageUrl = baseUrl + "/" + relativePath;
        
        // TODO: 如果有puzzleId，可以关联到谜题表
        
        log.info("Puzzle image uploaded by user {}: {}", userId, imageUrl);
        return imageUrl;
    }

    /**
     * 上传通用图片
     */
    public String uploadImage(MultipartFile file, Long userId, String type) {
        // 验证文件
        validateImageFile(file);

        // 生成文件名和路径
        String fileName = generateFileName(file, type);
        String relativePath = type + "/" + fileName;
        
        // 保存文件
        String fullPath = saveFile(file, relativePath);
        
        // 生成访问URL
        String imageUrl = baseUrl + "/" + relativePath;
        
        log.info("Image uploaded by user {}, type {}: {}", userId, type, imageUrl);
        return imageUrl;
    }

    /**
     * 删除图片
     */
    public void deleteImage(String imageUrl, Long userId) {
        try {
            // 从URL中提取相对路径
            String relativePath = imageUrl.replace(baseUrl + "/", "");
            Path filePath = Paths.get(uploadPath, relativePath);
            
            // 检查文件是否存在
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("Image deleted by user {}: {}", userId, imageUrl);
            } else {
                log.warn("Image file not found for deletion: {}", imageUrl);
            }
        } catch (IOException e) {
            log.error("Failed to delete image: {}", imageUrl, e);
            throw new BusinessException("删除图片失败");
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的文件");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("文件大小不能超过5MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
            throw new BusinessException("只支持JPG、PNG、GIF、WebP格式的图片");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new BusinessException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            throw new BusinessException("不支持的文件格式");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(MultipartFile file, String prefix) {
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        
        // 生成唯一文件名：前缀_时间戳_UUID.扩展名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        
        return prefix + "_" + timestamp + "_" + uuid + extension;
    }

    /**
     * 保存文件到磁盘
     */
    private String saveFile(MultipartFile file, String relativePath) {
        try {
            // 创建完整路径
            Path fullPath = Paths.get(uploadPath, relativePath);
            
            // 创建目录
            Files.createDirectories(fullPath.getParent());
            
            // 保存文件
            file.transferTo(fullPath.toFile());
            
            return fullPath.toString();
        } catch (IOException e) {
            log.error("Failed to save file: {}", relativePath, e);
            throw new BusinessException("文件保存失败");
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.'));
    }
}
