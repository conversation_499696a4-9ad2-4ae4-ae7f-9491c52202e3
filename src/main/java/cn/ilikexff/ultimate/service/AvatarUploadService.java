package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.config.OSSConfig;
import cn.ilikexff.ultimate.exception.BusinessException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 用户头像上传服务
 * 集成阿里云OSS存储
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AvatarUploadService {

    private final UserService userService;
    private final OSSConfig ossConfig;

    // 支持的图片格式
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );

    // 支持的文件扩展名
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".webp"
    );

    // 最大文件大小 (2MB)
    private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;

    /**
     * 上传用户头像
     */
    public String uploadAvatar(MultipartFile file, Long userId) {
        // 验证文件
        validateImageFile(file);

        try {
            // 生成文件名和路径
            String fileName = generateAvatarFileName(file, userId);
            String objectKey = "avatars/" + fileName;

            // 上传到阿里云OSS
            String avatarUrl = uploadToOSS(file, objectKey);

            // 更新用户头像URL
            userService.updateUserInfo(userId, null, avatarUrl);

            log.info("Avatar uploaded successfully for user {}: {}", userId, avatarUrl);
            return avatarUrl;

        } catch (Exception e) {
            log.error("Failed to upload avatar for user {}", userId, e);
            throw new BusinessException("头像上传失败，请稍后重试");
        }
    }

    /**
     * 删除用户头像
     */
    public void deleteAvatar(String avatarUrl, Long userId) {
        try {
            // 从URL中提取对象键
            String objectKey = extractObjectKeyFromUrl(avatarUrl);

            // 从阿里云OSS删除
            deleteFromOSS(objectKey);

            // 清空用户头像URL
            userService.updateUserInfo(userId, null, null);

            log.info("Avatar deleted successfully for user {}: {}", userId, avatarUrl);

        } catch (Exception e) {
            log.error("Failed to delete avatar for user {}: {}", userId, avatarUrl, e);
            throw new BusinessException("头像删除失败");
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的头像文件");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new BusinessException("头像文件大小不能超过2MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
            throw new BusinessException("只支持JPG、PNG、GIF、WebP格式的头像");
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new BusinessException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            throw new BusinessException("不支持的头像文件格式");
        }
    }

    /**
     * 生成头像文件名
     */
    private String generateAvatarFileName(MultipartFile file, Long userId) {
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);

        // 生成唯一文件名：user_{userId}_{时间戳}_{UUID}.扩展名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);

        return "user_" + userId + "_" + timestamp + "_" + uuid + extension;
    }

    /**
     * 上传文件到阿里云OSS
     */
    private String uploadToOSS(MultipartFile file, String objectKey) throws IOException {
        // 检查OSS配置是否完整
        if (ossConfig.getAccessKeyId() == null || ossConfig.getAccessKeyId().isEmpty() ||
            ossConfig.getAccessKeySecret() == null || ossConfig.getAccessKeySecret().isEmpty()) {
            log.warn("OSS credentials not configured, returning mock URL");
            return ossConfig.getBaseUrl() + "/" + objectKey;
        }

        try {
            // 创建OSS客户端
            OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(),
                objectKey,
                file.getInputStream()
            );
            ossClient.putObject(putObjectRequest);

            // 关闭客户端
            ossClient.shutdown();

            // 返回文件访问URL
            String fileUrl = ossConfig.getBaseUrl() + "/" + objectKey;
            log.info("File uploaded to OSS successfully: {}", fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("Failed to upload to OSS: {}", objectKey, e);
            throw new IOException("OSS上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从阿里云OSS删除文件
     */
    private void deleteFromOSS(String objectKey) {
        // 检查OSS配置是否完整
        if (ossConfig.getAccessKeyId() == null || ossConfig.getAccessKeyId().isEmpty() ||
            ossConfig.getAccessKeySecret() == null || ossConfig.getAccessKeySecret().isEmpty()) {
            log.warn("OSS credentials not configured, mock delete: {}", objectKey);
            return;
        }

        try {
            OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );
            ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
            ossClient.shutdown();

            log.info("File deleted from OSS successfully: {}", objectKey);
        } catch (Exception e) {
            log.error("Failed to delete from OSS: {}", objectKey, e);
            throw new RuntimeException("OSS删除失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取对象键
     */
    private String extractObjectKeyFromUrl(String url) {
        if (url == null || !url.startsWith(ossConfig.getBaseUrl())) {
            throw new BusinessException("无效的头像URL");
        }
        return url.replace(ossConfig.getBaseUrl() + "/", "");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.'));
    }
}
