package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.dto.LoginRequest;
import cn.ilikexff.ultimate.dto.RegisterRequest;
import cn.ilikexff.ultimate.dto.ResetPasswordRequest;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.UserMapper;
import cn.ilikexff.ultimate.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    private final UserService userService;
    private final VerificationCodeService verificationCodeService;

    @Value("${ultimate.register-reward-coins:100}")
    private Integer registerRewardCoins;

    /**
     * 用户注册
     */
    @Transactional
    public Map<String, Object> register(RegisterRequest request) {
        log.info("User registration attempt: {}", request.getUsername());

        // 验证密码确认
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 验证用户协议
        if (!Boolean.TRUE.equals(request.getAgreeTerms())) {
            throw new BusinessException("请同意用户协议");
        }

        // 检查用户名是否已存在
        if (userMapper.findByUsername(request.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (request.getEmail() != null && userMapper.findByEmail(request.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }

        // 检查手机号是否已存在
        if (request.getPhoneNumber() != null && userMapper.findByPhoneNumber(request.getPhoneNumber()) != null) {
            throw new BusinessException("手机号已被注册");
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setEmail(request.getEmail());
        user.setPhoneNumber(request.getPhoneNumber());
        user.setNickname(request.getNickname());
        user.setUCoins(registerRewardCoins);
        user.setReasoningPower(0);
        user.setRegistrationDate(LocalDateTime.now());
        user.setStatus("ACTIVE");
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        user.setDeleted(false);

        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new BusinessException("注册失败");
        }

        // 分配初始称号和勋章
        userService.assignInitialRewards(user.getUserId());

        // 生成JWT token
        String token = jwtUtil.generateJwtToken(user.getUsername());

        Map<String, Object> response = new HashMap<>();
        response.put("token", token);
        response.put("user", buildUserInfo(user));

        log.info("User registered successfully: {}", request.getUsername());
        return response;
    }

    /**
     * 用户登录
     */
    public Map<String, Object> login(LoginRequest request) {
        log.info("User login attempt: {}", request.getUsername());

        try {
            // 进行身份验证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 生成JWT token
            String token = jwtUtil.generateJwtToken(authentication);

            // 获取用户信息
            UserDetailsServiceImpl.UserPrincipal userPrincipal =
                    (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            User user = userPrincipal.getUser();

            // 更新登录信息
            userMapper.updateLoginInfo(user.getUserId());

            Map<String, Object> response = new HashMap<>();
            response.put("token", token);
            response.put("user", buildUserInfo(user));

            log.info("User logged in successfully: {}", request.getUsername());
            return response;

        } catch (Exception e) {
            log.warn("Login failed for user: {}, error: {}", request.getUsername(), e.getMessage());
            throw new BusinessException("用户名或密码错误");
        }
    }

    /**
     * 构建用户信息响应
     */
    private Map<String, Object> buildUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("username", user.getUsername());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("email", user.getEmail());
        userInfo.put("avatarUrl", user.getAvatarUrl());
        userInfo.put("uCoins", user.getUCoins());
        userInfo.put("reasoningPower", user.getReasoningPower());
        return userInfo;
    }

    /**
     * 重置密码
     */
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        log.info("Password reset attempt for email: {}", request.getEmail());

        // 验证确认密码
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new BusinessException("新密码与确认密码不一致");
        }

        // 验证验证码
        boolean isValid = verificationCodeService.verifyCode(
                request.getEmail(),
                request.getVerificationCode(),
                "RESET_PASSWORD"
        );

        if (!isValid) {
            throw new BusinessException("验证码无效或已过期");
        }

        // 查找用户
        User user = userMapper.findByEmail(request.getEmail());
        if (user == null) {
            throw new BusinessException("邮箱未注册");
        }

        // 更新密码
        String encodedPassword = passwordEncoder.encode(request.getNewPassword());
        int result = userMapper.updatePassword(user.getUserId(), encodedPassword);
        if (result <= 0) {
            throw new BusinessException("密码重置失败");
        }

        log.info("Password reset successfully for user: {}", user.getUsername());
    }
}
