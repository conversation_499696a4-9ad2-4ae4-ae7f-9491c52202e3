package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.config.UltimateConfig;
import cn.ilikexff.ultimate.entity.InvitationCode;
import cn.ilikexff.ultimate.entity.InvitationRecord;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.InvitationCodeMapper;
import cn.ilikexff.ultimate.mapper.InvitationRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 邀请码服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvitationService {

    private final InvitationCodeMapper invitationCodeMapper;
    private final InvitationRecordMapper invitationRecordMapper;
    private final UserService userService;
    private final UltimateConfig ultimateConfig;

    /**
     * 生成邀请码
     */
    @Transactional
    public InvitationCode generateInvitationCode(Long userId) {
        // 检查用户是否已有有效的邀请码
        List<InvitationCode> existingCodes = invitationCodeMapper.findByInviterUserId(userId);
        long activeCodes = existingCodes.stream()
                .filter(code -> code.getIsActive() && 
                               (code.getExpiresAt() == null || code.getExpiresAt().isAfter(LocalDateTime.now())))
                .count();

        if (activeCodes >= 3) { // 限制每个用户最多3个有效邀请码
            throw new BusinessException("您已有足够的有效邀请码，无需重复生成");
        }

        // 生成唯一邀请码
        String code = generateUniqueCode();

        InvitationCode invitationCode = new InvitationCode();
        invitationCode.setCode(code);
        invitationCode.setInviterUserId(userId);
        invitationCode.setUsedCount(0);
        invitationCode.setMaxUses(null); // 无限制
        invitationCode.setIsActive(true);
        invitationCode.setExpiresAt(null); // 永不过期
        invitationCode.setCreatedAt(LocalDateTime.now());
        invitationCode.setUpdatedAt(LocalDateTime.now());
        invitationCode.setDeleted(false);

        int result = invitationCodeMapper.insert(invitationCode);
        if (result <= 0) {
            throw new BusinessException("生成邀请码失败");
        }

        log.info("Generated invitation code: {} for user: {}", code, userId);
        return invitationCode;
    }

    /**
     * 验证邀请码
     */
    public InvitationCode validateInvitationCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null; // 邀请码为空，允许正常注册
        }

        InvitationCode invitationCode = invitationCodeMapper.findByCode(code.trim());
        if (invitationCode == null) {
            throw new BusinessException("邀请码不存在");
        }

        if (!invitationCode.getIsActive()) {
            throw new BusinessException("邀请码已失效");
        }

        if (invitationCode.getExpiresAt() != null && invitationCode.getExpiresAt().isBefore(LocalDateTime.now())) {
            throw new BusinessException("邀请码已过期");
        }

        if (invitationCode.getMaxUses() != null && invitationCode.getUsedCount() >= invitationCode.getMaxUses()) {
            throw new BusinessException("邀请码使用次数已达上限");
        }

        return invitationCode;
    }

    /**
     * 处理邀请注册
     */
    @Transactional
    public void processInvitationRegistration(String invitationCode, Long newUserId) {
        if (invitationCode == null || invitationCode.trim().isEmpty()) {
            return; // 没有邀请码，正常注册
        }

        InvitationCode invitation = validateInvitationCode(invitationCode);
        if (invitation == null) {
            return;
        }

        // 计算奖励
        Integer inviterReward = ultimateConfig.getInvitation().getRewardCoins();
        Integer inviteeReward = (int) (inviterReward * ultimateConfig.getInvitation().getInviteeRewardRatio());

        // 创建邀请记录
        InvitationRecord record = new InvitationRecord();
        record.setInvitationId(invitation.getInvitationId());
        record.setInviterUserId(invitation.getInviterUserId());
        record.setInviteeUserId(newUserId);
        record.setInviterReward(inviterReward);
        record.setInviteeReward(inviteeReward);
        record.setRewardStatus("PENDING");
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        record.setDeleted(false);

        int recordResult = invitationRecordMapper.insert(record);
        if (recordResult <= 0) {
            throw new BusinessException("创建邀请记录失败");
        }

        // 更新邀请码使用次数
        int updateResult = invitationCodeMapper.incrementUsedCount(invitation.getInvitationId());
        if (updateResult <= 0) {
            throw new BusinessException("更新邀请码使用次数失败");
        }

        // 发放奖励
        try {
            // 给邀请者发放奖励
            userService.addUserCoins(invitation.getInviterUserId(), inviterReward);
            
            // 给被邀请者发放奖励
            userService.addUserCoins(newUserId, inviteeReward);

            // 更新奖励状态为已完成
            invitationRecordMapper.updateRewardStatus(record.getRecordId(), "COMPLETED");

            log.info("Invitation rewards distributed: inviter={}, inviterReward={}, invitee={}, inviteeReward={}", 
                    invitation.getInviterUserId(), inviterReward, newUserId, inviteeReward);

        } catch (Exception e) {
            log.error("Failed to distribute invitation rewards", e);
            invitationRecordMapper.updateRewardStatus(record.getRecordId(), "FAILED");
            throw new BusinessException("发放邀请奖励失败");
        }
    }

    /**
     * 获取用户的邀请码列表
     */
    public List<InvitationCode> getUserInvitationCodes(Long userId) {
        return invitationCodeMapper.findByInviterUserId(userId);
    }

    /**
     * 获取用户的邀请记录
     */
    public List<InvitationRecord> getUserInvitationRecords(Long userId) {
        return invitationRecordMapper.findByInviterUserId(userId);
    }

    /**
     * 生成唯一邀请码
     */
    private String generateUniqueCode() {
        String code;
        int attempts = 0;
        do {
            code = generateRandomCode();
            attempts++;
            if (attempts > 100) {
                throw new BusinessException("生成邀请码失败，请稍后重试");
            }
        } while (invitationCodeMapper.existsByCode(code));
        
        return code;
    }

    /**
     * 生成随机邀请码
     */
    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < 8; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }
}
