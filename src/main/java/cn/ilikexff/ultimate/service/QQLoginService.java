package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.config.QQLoginConfig;
import cn.ilikexff.ultimate.entity.QQUserInfo;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.UserMapper;
import cn.ilikexff.ultimate.util.JwtUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * QQ登录服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QQLoginService {

    private final QQLoginConfig qqConfig;
    private final UserMapper userMapper;
    private final UserService userService;
    private final AchievementService achievementService;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;
    private final JwtUtil jwtUtil;

    /**
     * 获取QQ授权URL
     */
    public String getAuthorizationUrl(String state) {
        try {
            String encodedRedirectUri = URLEncoder.encode(qqConfig.getRedirectUri(), StandardCharsets.UTF_8);
            String encodedState = URLEncoder.encode(state, StandardCharsets.UTF_8);

            return String.format("%s?response_type=code&client_id=%s&redirect_uri=%s&scope=%s&state=%s",
                    qqConfig.getAuthorizeUrl(),
                    qqConfig.getAppId(),
                    encodedRedirectUri,
                    qqConfig.getScope(),
                    encodedState);
        } catch (Exception e) {
            log.error("Failed to generate QQ authorization URL", e);
            throw new BusinessException("生成QQ授权链接失败");
        }
    }

    /**
     * QQ登录处理
     */
    @Transactional
    public Map<String, Object> qqLogin(String code, String redirectUri) {
        try {
            // 1. 获取Access Token
            String accessToken = getAccessToken(code, redirectUri);

            // 2. 获取OpenID
            String openId = getOpenId(accessToken);

            // 3. 获取用户信息
            QQUserInfo qqUserInfo = getUserInfo(accessToken, openId);

            // 4. 查找或创建用户
            User user = findOrCreateUser(qqUserInfo, openId);

            // 5. 生成JWT Token
            String token = jwtUtil.generateJwtToken(user.getUsername());

            // 6. 更新登录信息
            userService.updateLoginInfo(user.getUserId());

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);
            result.put("isNewUser", user.getCreatedAt().isAfter(LocalDateTime.now().minusMinutes(1)));

            log.info("QQ login successful: userId={}, openId={}", user.getUserId(), openId);
            return result;

        } catch (Exception e) {
            log.error("QQ login failed: code={}", code, e);
            throw new BusinessException("QQ登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取Access Token
     */
    private String getAccessToken(String code, String redirectUri) {
        try {
            WebClient webClient = webClientBuilder.build();

            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path(qqConfig.getAccessTokenUrl())
                            .queryParam("grant_type", "authorization_code")
                            .queryParam("client_id", qqConfig.getAppId())
                            .queryParam("client_secret", qqConfig.getAppKey())
                            .queryParam("code", code)
                            .queryParam("redirect_uri", redirectUri)
                            .build())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            // 解析响应 access_token=xxx&expires_in=xxx&refresh_token=xxx
            if (response != null && response.contains("access_token=")) {
                Pattern pattern = Pattern.compile("access_token=([^&]+)");
                Matcher matcher = pattern.matcher(response);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

            throw new BusinessException("获取QQ Access Token失败");

        } catch (Exception e) {
            log.error("Failed to get QQ access token", e);
            throw new BusinessException("获取QQ Access Token失败");
        }
    }

    /**
     * 获取OpenID
     */
    private String getOpenId(String accessToken) {
        try {
            WebClient webClient = webClientBuilder.build();

            // 使用配置中的checkTokenUrl，如果没有配置则使用默认URL
            String checkUrl = qqConfig.getCheckTokenUrl() != null ?
                qqConfig.getCheckTokenUrl() : "https://graph.qq.com/oauth2.0/me";

            String response = webClient.get()
                    .uri(checkUrl.replace("{access_token}", accessToken))
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            // 解析JSONP响应 callback( {"client_id":"xxx","openid":"xxx"} );
            if (response != null && response.contains("openid")) {
                Pattern pattern = Pattern.compile("\"openid\":\"([^\"]+)\"");
                Matcher matcher = pattern.matcher(response);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }

            throw new BusinessException("获取QQ OpenID失败");

        } catch (Exception e) {
            log.error("Failed to get QQ openid", e);
            throw new BusinessException("获取QQ OpenID失败");
        }
    }

    /**
     * 获取用户信息
     */
    private QQUserInfo getUserInfo(String accessToken, String openId) {
        try {
            WebClient webClient = webClientBuilder.build();

            // 使用配置中的userInfoUrl，如果没有配置则使用默认URL
            String userInfoUrl = qqConfig.getUserInfoUrl() != null ?
                qqConfig.getUserInfoUrl() : "https://graph.qq.com/user/get_user_info";

            // 替换URL中的占位符
            String finalUrl = userInfoUrl
                .replace("{openid}", openId)
                .replace("{access_token}", accessToken)
                .replace("{oauth_consumer_key}", qqConfig.getAppId());

            String response = webClient.get()
                    .uri(finalUrl)
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            if (response != null) {
                QQUserInfo userInfo = objectMapper.readValue(response, QQUserInfo.class);
                userInfo.setOpenid(openId);

                if (userInfo.getRet() != null && userInfo.getRet() == 0) {
                    return userInfo;
                } else {
                    throw new BusinessException("获取QQ用户信息失败：" + userInfo.getMsg());
                }
            }

            throw new BusinessException("获取QQ用户信息失败");

        } catch (Exception e) {
            log.error("Failed to get QQ user info", e);
            throw new BusinessException("获取QQ用户信息失败");
        }
    }

    /**
     * 查找或创建用户
     */
    private User findOrCreateUser(QQUserInfo qqUserInfo, String openId) {
        // 1. 先根据QQ OpenID查找用户
        User existingUser = userMapper.findByQQOpenId(openId);
        if (existingUser != null) {
            return existingUser;
        }

        // 2. 创建新用户
        User newUser = new User();
        newUser.setUsername(generateUniqueUsername(qqUserInfo.getNickname()));
        newUser.setNickname(qqUserInfo.getNickname());
        newUser.setQqOpenid(openId);
        newUser.setAvatarUrl(qqUserInfo.getFigureurl_qq_2()); // 使用100x100的QQ头像
        newUser.setUCoins(100); // 新用户奖励
        newUser.setReasoningPower(0);
        newUser.setStatus("ACTIVE");

        int result = userMapper.insert(newUser);
        if (result <= 0) {
            throw new BusinessException("创建用户失败");
        }

        // 3. 检查新用户成就
        achievementService.checkRegistrationAchievements(newUser.getUserId());

        log.info("Created new user from QQ login: userId={}, openId={}, nickname={}",
                newUser.getUserId(), openId, qqUserInfo.getNickname());

        return newUser;
    }

    /**
     * 生成唯一用户名
     */
    private String generateUniqueUsername(String nickname) {
        String baseUsername = "qq_" + (nickname != null ? nickname.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "") : "user");

        // 限制长度
        if (baseUsername.length() > 15) {
            baseUsername = baseUsername.substring(0, 15);
        }

        String username = baseUsername;
        int suffix = 1;

        // 确保用户名唯一
        while (userMapper.existsByUsername(username)) {
            username = baseUsername + "_" + suffix;
            suffix++;

            // 防止无限循环
            if (suffix > 9999) {
                username = "qq_user_" + System.currentTimeMillis();
                break;
            }
        }

        return username;
    }

    /**
     * 通过授权码获取QQ OpenID（用于绑定）
     */
    public String getQQOpenIdByCode(String code, String redirectUri) {
        try {
            // 1. 获取Access Token
            String accessToken = getAccessToken(code, redirectUri);

            // 2. 获取OpenID
            return getOpenId(accessToken);

        } catch (Exception e) {
            log.error("Failed to get QQ OpenID by code: code={}", code, e);
            throw new BusinessException("获取QQ OpenID失败：" + e.getMessage());
        }
    }
}
