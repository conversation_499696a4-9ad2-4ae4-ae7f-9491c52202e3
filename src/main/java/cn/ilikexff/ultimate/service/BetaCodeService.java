package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.config.UltimateConfig;
import cn.ilikexff.ultimate.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 内测码服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BetaCodeService {

    private final UltimateConfig ultimateConfig;

    /**
     * 检查是否开启内测模式
     */
    public boolean isBetaModeEnabled() {
        return ultimateConfig.getBeta().getEnabled() != null && ultimateConfig.getBeta().getEnabled();
    }

    /**
     * 验证内测码
     */
    public boolean validateBetaCode(String betaCode) {
        // 如果未开启内测模式，直接返回true
        if (!isBetaModeEnabled()) {
            return true;
        }

        // 如果开启了内测模式但没有提供内测码，返回false
        if (betaCode == null || betaCode.trim().isEmpty()) {
            return false;
        }

        // 获取配置的内测码列表
        List<String> validCodes = ultimateConfig.getBeta().getCodes();
        if (validCodes == null || validCodes.isEmpty()) {
            log.warn("Beta mode is enabled but no beta codes are configured");
            return false;
        }

        // 验证内测码是否在有效列表中
        boolean isValid = validCodes.contains(betaCode.trim());
        
        if (isValid) {
            log.info("Valid beta code used: {}", betaCode);
        } else {
            log.warn("Invalid beta code attempted: {}", betaCode);
        }

        return isValid;
    }

    /**
     * 验证内测码并抛出异常（用于注册时验证）
     */
    public void validateBetaCodeForRegistration(String betaCode) {
        if (!isBetaModeEnabled()) {
            return; // 未开启内测模式，无需验证
        }

        if (betaCode == null || betaCode.trim().isEmpty()) {
            throw new BusinessException("当前为内测期间，请输入内测码");
        }

        if (!validateBetaCode(betaCode)) {
            throw new BusinessException("内测码无效，请检查后重试");
        }
    }

    /**
     * 获取内测模式状态信息
     */
    public BetaStatus getBetaStatus() {
        BetaStatus status = new BetaStatus();
        status.setEnabled(isBetaModeEnabled());
        status.setMessage(isBetaModeEnabled() ? "当前为内测期间，需要内测码才能注册" : "开放注册中");
        return status;
    }

    /**
     * 内测状态信息
     */
    public static class BetaStatus {
        private Boolean enabled;
        private String message;

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
