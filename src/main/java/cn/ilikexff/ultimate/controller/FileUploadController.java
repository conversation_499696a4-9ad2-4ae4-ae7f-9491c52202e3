package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.service.FileUploadService;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@Tag(name = "文件上传", description = "文件上传相关接口")
@Slf4j
@RestController
@RequestMapping("/api/upload")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class FileUploadController {

    private final FileUploadService fileUploadService;

    /**
     * 上传用户头像
     */
    @Operation(summary = "上传用户头像", description = "上传并设置用户头像")
    @ApiResponse(responseCode = "200", description = "上传成功", 
                content = @Content(schema = @Schema(implementation = Result.class)))
    @PostMapping(value = "/avatar", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, Object>> uploadAvatar(
            @Parameter(description = "头像文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        Long userId = getCurrentUserId();
        
        // 上传头像并更新用户信息
        String avatarUrl = fileUploadService.uploadAvatar(file, userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("avatarUrl", avatarUrl);
        result.put("message", "头像上传成功");
        
        log.info("Avatar uploaded successfully for user: {}, url: {}", userId, avatarUrl);
        return Result.success("头像上传成功", result);
    }

    /**
     * 上传谜题图片
     */
    @Operation(summary = "上传谜题图片", description = "上传谜题相关图片")
    @ApiResponse(responseCode = "200", description = "上传成功")
    @PostMapping(value = "/puzzle-image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, Object>> uploadPuzzleImage(
            @Parameter(description = "图片文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "谜题ID（可选，用于关联）")
            @RequestParam(value = "puzzleId", required = false) Long puzzleId) {
        
        Long userId = getCurrentUserId();
        
        // 上传谜题图片
        String imageUrl = fileUploadService.uploadPuzzleImage(file, userId, puzzleId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("imageUrl", imageUrl);
        result.put("message", "图片上传成功");
        
        log.info("Puzzle image uploaded successfully by user: {}, url: {}", userId, imageUrl);
        return Result.success("图片上传成功", result);
    }

    /**
     * 上传通用图片
     */
    @Operation(summary = "上传通用图片", description = "上传通用图片文件")
    @ApiResponse(responseCode = "200", description = "上传成功")
    @PostMapping(value = "/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, Object>> uploadImage(
            @Parameter(description = "图片文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "图片类型", example = "avatar|puzzle|badge|other")
            @RequestParam(value = "type", defaultValue = "other") String type) {
        
        Long userId = getCurrentUserId();
        
        // 上传通用图片
        String imageUrl = fileUploadService.uploadImage(file, userId, type);
        
        Map<String, Object> result = new HashMap<>();
        result.put("imageUrl", imageUrl);
        result.put("type", type);
        result.put("message", "图片上传成功");
        
        log.info("Image uploaded successfully by user: {}, type: {}, url: {}", userId, type, imageUrl);
        return Result.success("图片上传成功", result);
    }

    /**
     * 删除图片
     */
    @Operation(summary = "删除图片", description = "删除已上传的图片")
    @ApiResponse(responseCode = "200", description = "删除成功")
    @DeleteMapping("/image")
    public Result<Void> deleteImage(
            @Parameter(description = "图片URL", required = true)
            @RequestParam String imageUrl) {
        
        Long userId = getCurrentUserId();
        
        // 删除图片
        fileUploadService.deleteImage(imageUrl, userId);
        
        log.info("Image deleted successfully by user: {}, url: {}", userId, imageUrl);
        return Result.success("图片删除成功");
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsServiceImpl.UserPrincipal) {
            UserDetailsServiceImpl.UserPrincipal userPrincipal =
                    (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUser().getUserId();
        }
        throw new RuntimeException("用户未登录");
    }
}
