package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.dto.LoginRequest;
import cn.ilikexff.ultimate.dto.QQLoginRequest;
import cn.ilikexff.ultimate.dto.RegisterRequest;
import cn.ilikexff.ultimate.service.AuthService;
import cn.ilikexff.ultimate.service.QQLoginService;
import cn.ilikexff.ultimate.service.UserService;
import cn.ilikexff.ultimate.service.VerificationCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 认证控制器
 */
@Tag(name = "认证管理", description = "用户认证相关接口")
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthService authService;
    private final UserService userService;
    private final VerificationCodeService verificationCodeService;

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册", description = "新用户注册账号")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "注册成功",
                    content = @Content(schema = @Schema(implementation = Result.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/register")
    public Result<Map<String, Object>> register(
            @Parameter(description = "注册请求信息", required = true)
            @Valid @RequestBody RegisterRequest request) {
        log.info("User registration request: {}", request.getUsername());

        Map<String, Object> result = authService.register(request);
        return Result.success("注册成功", result);
    }

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户登录获取访问令牌")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(schema = @Schema(implementation = Result.class))),
            @ApiResponse(responseCode = "401", description = "用户名或密码错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/login")
    public Result<Map<String, Object>> login(
            @Parameter(description = "登录请求信息", required = true)
            @Valid @RequestBody LoginRequest request) {
        log.info("User login request: {}", request.getUsername());

        Map<String, Object> result = authService.login(request);
        return Result.success("登录成功", result);
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出", description = "用户登出系统")
    @ApiResponse(responseCode = "200", description = "登出成功")
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，客户端删除token即可
        return Result.success("登出成功");
    }

    /**
     * 检查用户名是否可用
     */
    @Operation(summary = "检查用户名", description = "检查用户名是否可用")
    @ApiResponse(responseCode = "200", description = "检查成功")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(
            @Parameter(description = "用户名", required = true)
            @RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);
        String message = available ? "用户名可用" : "用户名已被使用";
        return Result.success(message, available);
    }

    /**
     * 检查邮箱是否可用
     */
    @Operation(summary = "检查邮箱", description = "检查邮箱是否可用")
    @ApiResponse(responseCode = "200", description = "检查成功")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(
            @Parameter(description = "邮箱地址", required = true)
            @RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);
        String message = available ? "邮箱可用" : "邮箱已被使用";
        return Result.success(message, available);
    }

    /**
     * 发送验证码
     */
    @Operation(summary = "发送验证码", description = "发送短信或邮箱验证码")
    @ApiResponse(responseCode = "200", description = "发送成功")
    @PostMapping("/send-code")
    public Result<Void> sendVerificationCode(
            @Parameter(description = "目标手机号或邮箱", required = true)
            @RequestParam String target,
            @Parameter(description = "验证码类型：SMS/EMAIL", required = true)
            @RequestParam String type,
            @Parameter(description = "验证码用途：REGISTER/RESET_PASSWORD/BIND_PHONE/BIND_EMAIL", required = true)
            @RequestParam(defaultValue = "REGISTER") String purpose) {

        verificationCodeService.sendVerificationCode(target, type.toUpperCase(), purpose.toUpperCase());
        log.info("Send verification code: target={}, type={}, purpose={}", target, type, purpose);
        return Result.success("验证码已发送");
    }
}
