package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.dto.BindAccountRequest;
import cn.ilikexff.ultimate.dto.ChangePasswordRequest;
import cn.ilikexff.ultimate.dto.UpdateProfileRequest;
import cn.ilikexff.ultimate.dto.UserProfileResponse;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.entity.UserPuzzleProgress;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import cn.ilikexff.ultimate.service.UserService;
import cn.ilikexff.ultimate.service.UserPuzzleProgressService;
import cn.ilikexff.ultimate.service.HintService;
import cn.ilikexff.ultimate.service.QQLoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@Slf4j
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class UserController {

    private final UserService userService;
    private final UserPuzzleProgressService progressService;
    private final HintService hintService;

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/profile")
    public Result<UserProfileResponse> getCurrentUserProfile() {
        Long userId = getCurrentUserId();
        UserProfileResponse profile = userService.getUserProfile(userId);
        return Result.success(profile);
    }

    /**
     * 获取指定用户信息
     */
    @Operation(summary = "获取指定用户信息", description = "根据用户ID获取用户详细信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/profile/{userId}")
    public Result<UserProfileResponse> getUserProfile(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        UserProfileResponse profile = userService.getUserProfile(userId);
        return Result.success(profile);
    }

    /**
     * 更新用户资料
     */
    @Operation(summary = "更新用户资料", description = "更新当前用户的基本资料信息")
    @ApiResponse(responseCode = "200", description = "更新成功")
    @PutMapping("/profile")
    public Result<Void> updateProfile(
            @Parameter(description = "更新资料请求", required = true)
            @Valid @RequestBody UpdateProfileRequest request) {
        Long userId = getCurrentUserId();
        userService.updateProfile(userId, request);
        return Result.success("用户资料更新成功");
    }

    /**
     * 获取用户U币余额
     */
    @GetMapping("/coins")
    public Result<Map<String, Object>> getUserCoins() {
        Long userId = getCurrentUserId();
        User user = userService.getUserById(userId);

        Map<String, Object> result = new HashMap<>();
        result.put("uCoins", user.getUCoins());
        result.put("reasoningPower", user.getReasoningPower());

        return Result.success(result);
    }

    /**
     * 每日签到
     */
    @PostMapping("/daily-signin")
    public Result<Map<String, Object>> dailySignin() {
        // TODO: 实现每日签到逻辑
        Long userId = getCurrentUserId();

        Map<String, Object> result = new HashMap<>();
        result.put("reward", 10);
        result.put("message", "签到成功，获得10个U币");

        return Result.success("签到成功", result);
    }

    /**
     * 获取用户成就统计
     */
    @GetMapping("/achievements")
    public Result<Map<String, Object>> getUserAchievements() {
        Long userId = getCurrentUserId();

        // TODO: 实现成就统计逻辑
        Map<String, Object> achievements = new HashMap<>();
        achievements.put("completedPuzzles", 0);
        achievements.put("totalReasoningPower", 0);
        achievements.put("titlesCount", 0);
        achievements.put("badgesCount", 0);

        return Result.success(achievements);
    }

    /**
     * 获取用户谜题进度列表
     */
    @Operation(summary = "获取用户谜题进度", description = "获取当前用户的所有谜题进度")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/puzzle-progress")
    public Result<List<UserPuzzleProgress>> getUserPuzzleProgress() {
        Long userId = getCurrentUserId();
        List<UserPuzzleProgress> progressList = progressService.getUserAllProgress(userId);
        return Result.success(progressList);
    }

    /**
     * 获取用户已完成的谜题
     */
    @Operation(summary = "获取已完成谜题", description = "获取当前用户已完成的谜题列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/completed-puzzles")
    public Result<List<UserPuzzleProgress>> getCompletedPuzzles() {
        Long userId = getCurrentUserId();
        List<UserPuzzleProgress> completedList = progressService.getUserCompletedProgress(userId);
        return Result.success(completedList);
    }

    /**
     * 获取用户进行中的谜题
     */
    @Operation(summary = "获取进行中谜题", description = "获取当前用户正在进行的谜题列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/in-progress-puzzles")
    public Result<List<UserPuzzleProgress>> getInProgressPuzzles() {
        Long userId = getCurrentUserId();
        List<UserPuzzleProgress> inProgressList = progressService.getUserInProgressProgress(userId);
        return Result.success(inProgressList);
    }

    /**
     * 获取用户购买的提示记录
     */
    @Operation(summary = "获取购买的提示", description = "获取当前用户购买的所有提示记录")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/purchased-hints")
    public Result<List<cn.ilikexff.ultimate.entity.UserHintLog>> getPurchasedHints() {
        Long userId = getCurrentUserId();
        var hintLogs = hintService.getUserPurchasedHints(userId);
        return Result.success(hintLogs);
    }

    /**
     * 获取用户在指定谜题上的提示统计
     */
    @Operation(summary = "获取谜题提示统计", description = "获取用户在指定谜题上的提示使用统计")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/puzzle/{puzzleId}/hint-statistics")
    public Result<HintService.HintStatistics> getPuzzleHintStatistics(
            @Parameter(description = "谜题ID", required = true)
            @PathVariable Long puzzleId) {
        Long userId = getCurrentUserId();
        var statistics = hintService.getUserPuzzleHintStatistics(userId, puzzleId);
        return Result.success(statistics);
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsServiceImpl.UserPrincipal) {
            UserDetailsServiceImpl.UserPrincipal userPrincipal =
                    (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUser().getUserId();
        }
        throw new RuntimeException("用户未登录");
    }

    /**
     * 修改密码
     */
    @Operation(summary = "修改密码", description = "修改当前用户的登录密码")
    @ApiResponse(responseCode = "200", description = "修改成功")
    @PostMapping("/change-password")
    public Result<Void> changePassword(
            @Parameter(description = "修改密码请求", required = true)
            @Valid @RequestBody ChangePasswordRequest request) {
        Long userId = getCurrentUserId();
        userService.changePassword(userId, request);
        return Result.success("密码修改成功");
    }

    /**
     * 绑定邮箱或手机号
     */
    @Operation(summary = "绑定账号", description = "绑定邮箱或手机号到当前账户")
    @ApiResponse(responseCode = "200", description = "绑定成功")
    @PostMapping("/bind-account")
    public Result<Void> bindAccount(
            @Parameter(description = "绑定账号请求", required = true)
            @Valid @RequestBody BindAccountRequest request) {
        Long userId = getCurrentUserId();
        userService.bindAccount(userId, request);
        return Result.success("账号绑定成功");
    }

    /**
     * 解绑邮箱或手机号
     */
    @Operation(summary = "解绑账号", description = "解绑邮箱或手机号")
    @ApiResponse(responseCode = "200", description = "解绑成功")
    @PostMapping("/unbind-account")
    public Result<Void> unbindAccount(
            @Parameter(description = "绑定类型：EMAIL/PHONE", required = true)
            @RequestParam String bindType) {
        Long userId = getCurrentUserId();
        userService.unbindAccount(userId, bindType);
        return Result.success("账号解绑成功");
    }

    /**
     * 绑定QQ账号
     */
    @Operation(summary = "绑定QQ账号", description = "将QQ账号绑定到当前账户")
    @ApiResponse(responseCode = "200", description = "绑定成功")
    @PostMapping("/bind-qq")
    public Result<Void> bindQQAccount(
            @Parameter(description = "QQ授权码", required = true)
            @RequestParam String code,
            @Parameter(description = "回调地址", required = true)
            @RequestParam String redirectUri) {
        // TODO: 实现QQ账号绑定逻辑
        return Result.success("QQ账号绑定成功");
    }

    /**
     * 解绑QQ账号
     */
    @Operation(summary = "解绑QQ账号", description = "解绑当前账户的QQ账号")
    @ApiResponse(responseCode = "200", description = "解绑成功")
    @PostMapping("/unbind-qq")
    public Result<Void> unbindQQAccount() {
        // TODO: 实现QQ账号解绑逻辑
        return Result.success("QQ账号解绑成功");
    }
}
