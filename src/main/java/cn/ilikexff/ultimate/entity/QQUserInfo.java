package cn.ilikexff.ultimate.entity;

import lombok.Data;

/**
 * QQ用户信息实体类
 */
@Data
public class QQUserInfo {
    
    private String openid;
    private String nickname;
    private String figureurl; // 头像URL (30x30)
    private String figureurl_1; // 头像URL (50x50)
    private String figureurl_2; // 头像URL (100x100)
    private String figureurl_qq_1; // QQ头像URL (40x40)
    private String figureurl_qq_2; // QQ头像URL (100x100)
    private String gender; // 性别
    private String province; // 省份
    private String city; // 城市
    private String year; // 出生年
    private String constellation; // 星座
    private Integer ret; // 返回码
    private String msg; // 返回消息
}
