package cn.ilikexff.ultimate.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邀请码实体
 */
@Data
public class InvitationCode {
    
    /**
     * 邀请码ID
     */
    private Long invitationId;
    
    /**
     * 邀请码
     */
    private String code;
    
    /**
     * 邀请者用户ID
     */
    private Long inviterUserId;
    
    /**
     * 使用次数
     */
    private Integer usedCount = 0;
    
    /**
     * 最大使用次数（null表示无限制）
     */
    private Integer maxUses;
    
    /**
     * 是否有效
     */
    private Boolean isActive = true;
    
    /**
     * 过期时间（null表示永不过期）
     */
    private LocalDateTime expiresAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否删除
     */
    private Boolean deleted = false;
}
