package cn.ilikexff.ultimate.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 邀请记录实体
 */
@Data
public class InvitationRecord {
    
    /**
     * 记录ID
     */
    private Long recordId;
    
    /**
     * 邀请码ID
     */
    private Long invitationId;
    
    /**
     * 邀请者用户ID
     */
    private Long inviterUserId;
    
    /**
     * 被邀请者用户ID
     */
    private Long inviteeUserId;
    
    /**
     * 邀请者获得的奖励U币
     */
    private Integer inviterReward;
    
    /**
     * 被邀请者获得的奖励U币
     */
    private Integer inviteeReward;
    
    /**
     * 奖励状态：PENDING-待发放，COMPLETED-已发放，FAILED-发放失败
     */
    private String rewardStatus = "PENDING";
    
    /**
     * 创建时间（注册时间）
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否删除
     */
    private Boolean deleted = false;
}
