-- 邀请码表
CREATE TABLE IF NOT EXISTS invitation_codes (
    invitation_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '邀请码ID',
    code VARCHAR(20) NOT NULL UNIQUE COMMENT '邀请码',
    inviter_user_id BIGINT NOT NULL COMMENT '邀请者用户ID',
    used_count INT DEFAULT 0 COMMENT '使用次数',
    max_uses INT NULL COMMENT '最大使用次数（NULL表示无限制）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    expires_at DATETIME NULL COMMENT '过期时间（NULL表示永不过期）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    INDEX idx_code (code),
    INDEX idx_inviter_user_id (inviter_user_id),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (inviter_user_id) REFERENCES users(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请码表';

-- 邀请记录表
CREATE TABLE IF NOT EXISTS invitation_records (
    record_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    invitation_id BIGINT NOT NULL COMMENT '邀请码ID',
    inviter_user_id BIGINT NOT NULL COMMENT '邀请者用户ID',
    invitee_user_id BIGINT NOT NULL COMMENT '被邀请者用户ID',
    inviter_reward INT NOT NULL COMMENT '邀请者获得的奖励U币',
    invitee_reward INT NOT NULL COMMENT '被邀请者获得的奖励U币',
    reward_status ENUM('PENDING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING' COMMENT '奖励状态',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（注册时间）',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    
    INDEX idx_invitation_id (invitation_id),
    INDEX idx_inviter_user_id (inviter_user_id),
    INDEX idx_invitee_user_id (invitee_user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_reward_status (reward_status),
    
    FOREIGN KEY (invitation_id) REFERENCES invitation_codes(invitation_id),
    FOREIGN KEY (inviter_user_id) REFERENCES users(user_id),
    FOREIGN KEY (invitee_user_id) REFERENCES users(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邀请记录表';
