# 应用基础配置
spring:
  application:
    name: Ultimate

  # 数据源配置
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: 5247xff
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  # 邮件配置
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: AOYVJZWOIHSOGJUP
    default-encoding: UTF-8
    protocol: smtp
    port: 465
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465
          ssl:
            enable: true
          starttls:
            enable: true
            required: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.ilikexff.ultimate.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 服务器配置
server:
  port: 8080

# 日志配置
logging:
  level:
    cn.ilikexff.ultimate: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: UltimateReasoningSecretKey2024ForJWTTokenGeneration
  expiration: 86400000

# QQ登录配置
qq:
  app-id: 102096510
  app-key: your_qq_app_key  # 请补充你的QQ应用密钥
  redirect-uri: http://localhost:8080/auth/qq/callback
  scope: get_user_info
  check-token-url: https://graph.qq.com/oauth2.0/me?access_token={access_token}
  user-info-url: https://graph.qq.com/user/get_user_info?openid={openid}&access_token={access_token}&oauth_consumer_key={oauth_consumer_key}

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: your_access_key_id
    access-key-secret: your_access_key_secret
    bucket-name: ultimate-reasoning
    base-url: https://ultimate-reasoning.oss-cn-hangzhou.aliyuncs.com

# 业务配置
ultimate:
  # 注册奖励U币
  register-reward-coins: 100
  # 每日签到基础奖励
  daily-signin-base-reward: 10
  # 每日一推额外奖励倍数
  daily-puzzle-bonus-multiplier: 1.5

  # 内测配置
  beta:
    # 是否开启内测模式（开启后需要内测码才能注册）
    enabled: true
    # 内测码列表（可配置多个）
    codes:
      - "ULTIMATE2024"
      - "BETA_TEST_001"
      - "REASONING_BETA"

  # 邀请码配置
  invitation:
    # 邀请奖励U币（邀请者获得的奖励）
    reward-coins: 200
    # 被邀请者奖励比例（邀请者奖励的25%）
    invitee-reward-ratio: 0.25

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: cn.ilikexff.ultimate.controller
