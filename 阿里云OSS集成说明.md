# 阿里云OSS集成说明

## 📋 概述

Ultimate推理社的图片上传功能已经设计为使用阿里云OSS（对象存储服务）。目前只有用户头像上传功能需要用户操作，其他图片（谜题图片、勋章图标、称号图标）都通过管理后台管理。

## 🎯 图片使用场景

### ✅ 用户可上传
- **用户头像** - 用户在个人资料中上传头像

### 🔧 管理后台管理
- **谜题图片** - 谜题内容相关图片
- **勋章图标** - 系统预设勋章图标
- **称号图标** - 系统预设称号图标

## 🛠️ 集成步骤

### 1. 添加阿里云OSS依赖

在 `build.gradle` 中添加：

```gradle
dependencies {
    // 阿里云OSS SDK
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.17.4'
}
```

### 2. 配置阿里云OSS

在 `application.yml` 中配置（已添加）：

```yaml
# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: your_access_key_id
    access-key-secret: your_access_key_secret
    bucket-name: ultimate-reasoning
    base-url: https://ultimate-reasoning.oss-cn-hangzhou.aliyuncs.com
```

### 3. 完善AvatarUploadService

需要在 `AvatarUploadService.java` 中完善OSS集成代码：

```java
// 添加依赖注入
private final OSSConfig ossConfig;

// 完善uploadToOSS方法
private String uploadToOSS(MultipartFile file, String objectKey) throws IOException {
    try {
        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build(
            ossConfig.getEndpoint(), 
            ossConfig.getAccessKeyId(), 
            ossConfig.getAccessKeySecret()
        );
        
        // 上传文件
        PutObjectRequest putObjectRequest = new PutObjectRequest(
            ossConfig.getBucketName(), 
            objectKey, 
            file.getInputStream()
        );
        ossClient.putObject(putObjectRequest);
        
        // 关闭客户端
        ossClient.shutdown();
        
        // 返回文件访问URL
        return ossConfig.getBaseUrl() + "/" + objectKey;
        
    } catch (Exception e) {
        log.error("Failed to upload to OSS: {}", objectKey, e);
        throw new IOException("OSS上传失败", e);
    }
}

// 完善deleteFromOSS方法
private void deleteFromOSS(String objectKey) {
    try {
        OSS ossClient = new OSSClientBuilder().build(
            ossConfig.getEndpoint(), 
            ossConfig.getAccessKeyId(), 
            ossConfig.getAccessKeySecret()
        );
        ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
        ossClient.shutdown();
    } catch (Exception e) {
        log.error("Failed to delete from OSS: {}", objectKey, e);
        throw new RuntimeException("OSS删除失败", e);
    }
}
```

### 4. 阿里云OSS设置

1. **创建OSS Bucket**
   - 登录阿里云控制台
   - 创建名为 `ultimate-reasoning` 的Bucket
   - 设置读写权限为公共读

2. **获取访问密钥**
   - 创建RAM用户
   - 授予OSS相关权限
   - 获取AccessKey ID和AccessKey Secret

3. **配置跨域规则**（如果前端直传需要）
   ```json
   {
     "allowedOrigins": ["*"],
     "allowedMethods": ["GET", "POST", "PUT", "DELETE"],
     "allowedHeaders": ["*"],
     "exposeHeaders": ["ETag"],
     "maxAgeSeconds": 3600
   }
   ```

## 📊 API接口

### 用户头像上传
- **接口**: `POST /api/user/avatar`
- **参数**: `file` (MultipartFile)
- **返回**: 头像URL

### 用户头像删除
- **接口**: `DELETE /api/user/avatar`
- **返回**: 删除成功消息

## 🔒 安全考虑

1. **文件类型限制**
   - 只允许图片格式：JPG、PNG、GIF、WebP
   - 文件大小限制：2MB

2. **文件名安全**
   - 使用UUID生成唯一文件名
   - 包含用户ID和时间戳

3. **访问控制**
   - 用户只能上传/删除自己的头像
   - 需要登录认证

## 💰 成本优化

1. **存储优化**
   - 定期清理无效图片
   - 使用图片压缩

2. **访问优化**
   - 配置CDN加速
   - 设置合理的缓存策略

## 🚀 部署建议

1. **生产环境配置**
   ```yaml
   aliyun:
     oss:
       endpoint: https://oss-cn-hangzhou.aliyuncs.com
       access-key-id: ${ALIYUN_OSS_ACCESS_KEY_ID}
       access-key-secret: ${ALIYUN_OSS_ACCESS_KEY_SECRET}
       bucket-name: ultimate-reasoning-prod
       base-url: https://cdn.ultimate-reasoning.com
   ```

2. **环境变量设置**
   - 使用环境变量存储敏感信息
   - 不要将密钥提交到代码仓库

## 📝 管理后台图片管理

对于谜题图片、勋章图标等管理后台的图片：

1. **建议使用专门的管理后台系统**
2. **可以使用相同的OSS存储**
3. **按类型分目录存储**：
   - `puzzles/` - 谜题相关图片
   - `badges/` - 勋章图标
   - `titles/` - 称号图标
   - `avatars/` - 用户头像

## ✅ 当前状态

- ✅ 用户头像上传接口已实现
- ✅ OSS配置类已创建
- ✅ 文件验证和安全检查已实现
- ⚠️ 需要添加阿里云OSS SDK依赖
- ⚠️ 需要完善OSS上传/删除代码
- ⚠️ 需要配置真实的OSS参数

## 🔄 下一步

1. 添加阿里云OSS SDK依赖
2. 配置真实的OSS参数
3. 完善OSS上传/删除代码
4. 测试头像上传功能
5. 开发管理后台的图片管理功能
