plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.0'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'cn.ilikexff'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot 核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // 数据库相关
    runtimeOnly 'com.mysql:mysql-connector-j'

    // MyBatis支持
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'

    // JWT 支持
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

    // HTTP 客户端（用于QQ登录）
    implementation 'org.springframework.boot:spring-boot-starter-webflux'

    // 邮件服务
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    // JSON 处理
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // SpringDoc OpenAPI 3
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0'

    // 阿里云OSS SDK
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.17.4'

    // 工具类
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // 开发工具
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}

// 配置编译器选项
tasks.withType(JavaCompile) {
    options.compilerArgs += ['-Xlint:unchecked', '-Xlint:deprecation']
}
